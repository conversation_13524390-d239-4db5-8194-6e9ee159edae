import React from 'react';
import { View, Text, ScrollView, StyleSheet, Linking } from 'react-native';
import Header from '../components/Header';

const TermsConditionsScreen = () => {
  const currentYear = new Date().getFullYear();

  const handleOpenLink = (url: string) => {
    Linking.openURL(url);
  };

  return (
    <View style={styles.container}>
      <Header title="Terms & Conditions" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.sectionBox}>
          <Text style={styles.lastUpdated}>Last updated: June 17, 2025</Text>
          
          <Text style={styles.paragraph}>
            Welcome to Referee Tracker! These Terms and Conditions ("Terms") govern your use of our mobile application and services (collectively, the "Service").
          </Text>
          
          <Text style={styles.sectionTitle}>1. Acceptance of Terms</Text>
          <Text style={styles.paragraph}>
            By accessing or using the Service, you agree to be bound by these Terms. If you do not agree to these Terms, please do not use the Service.
          </Text>
          
          <Text style={styles.sectionTitle}>2. Use of the Service</Text>
          <Text style={styles.paragraph}>
            You may use the Service only for lawful purposes and in accordance with these Terms. You agree not to use the Service:
          </Text>
          <Text style={styles.listItem}>• In any way that violates any applicable law or regulation</Text>
          <Text style={styles.listItem}>• To transmit any malicious code or harmful content</Text>
          <Text style={styles.listItem}>• To interfere with or disrupt the integrity or performance of the Service</Text>
          
          <Text style={styles.sectionTitle}>3. User Accounts</Text>
          <Text style={styles.paragraph}>
            You may be required to create an account to access certain features of the Service. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.
          </Text>
          
          <Text style={styles.sectionTitle}>4. Intellectual Property</Text>
          <Text style={styles.paragraph}>
            The Service and its original content, features, and functionality are owned by Referee Tracker and are protected by international copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws.
          </Text>
          
          <Text style={styles.sectionTitle}>5. Limitation of Liability</Text>
          <Text style={styles.paragraph}>
            In no event shall Referee Tracker, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the Service.
          </Text>
          
          <Text style={styles.sectionTitle}>6. Changes to Terms</Text>
          <Text style={styles.paragraph}>
            We reserve the right to modify or replace these Terms at any time. We will provide notice of any changes by posting the updated Terms on this page and updating the "Last updated" date.
          </Text>
          
          <Text style={styles.sectionTitle}>7. Governing Law</Text>
          <Text style={styles.paragraph}>
            These Terms shall be governed and construed in accordance with the laws of [Your Country], without regard to its conflict of law provisions.
          </Text>
          
          <Text style={styles.sectionTitle}>8. Contact Us</Text>
          <Text style={[styles.paragraph, styles.noMarginBottom]}>
            If you have any questions about these Terms, please contact us at:
          </Text>
          <Text 
            style={styles.link}
            onPress={() => handleOpenLink('mailto:<EMAIL>')}
          >
            <EMAIL>
          </Text>
          
          <Text style={[styles.paragraph, styles.centered, styles.marginTop]}>
            © {currentYear} Referee Tracker. All rights reserved.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    overflow: 'hidden',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    padding: 16,
    marginTop: 8,
  },
  lastUpdated: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
    marginBottom: 16,
    textAlign: 'right',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#222',
    marginTop: 16,
    marginBottom: 8,
  },
  paragraph: {
    fontSize: 14,
    lineHeight: 22,
    color: '#444',
    marginBottom: 12,
  },
  listItem: {
    fontSize: 14,
    lineHeight: 22,
    color: '#444',
    marginLeft: 16,
    marginBottom: 4,
  },
  link: {
    color: '#066b36',
    textDecorationLine: 'underline',
    marginTop: 4,
  },
  noMarginBottom: {
    marginBottom: 0,
  },
  centered: {
    textAlign: 'center',
  },
  marginTop: {
    marginTop: 24,
  },
});

export default TermsConditionsScreen;
