# Referee Management Platform - Setup Guide

## Project Structure
```
referee-management-platform/
├── mobile-app/                    # Root directory (current workspace)
│   ├── ref-api/                  # Backend API (NestJS)
│   ├── ref-app/                  # Mobile App (Expo/React Native)
│   ├── docker-compose.yml       # Services configuration
│   └── SETUP_GUIDE.md           # This file
```

## Services Overview
- **PostgreSQL**: Database (Port 5432)
- **Redis**: Caching & Sessions (Port 6379)
- **MinIO**: S3-compatible object storage (Port 9000/9001)
- **ref-api**: NestJS Backend API (Port 3000)
- **ref-app**: Expo/React Native Mobile App

## Prerequisites
- Node.js 22.16.0 (using nvm)
- Docker & Docker Compose
- WSL2 (for backend development)
- Git Bash (for mobile app development)

## Setup Instructions

### 1. Start Infrastructure Services
```bash
# In the root directory (mobile-app/)
docker compose up -d postgres redis minio
```

### 2. Setup Backend (ref-api) - Use WSL Terminal
```bash
cd ref-api

# Install dependencies (already done)
npm install

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma db push

# Start development server
npm run start:dev
```

### 3. Setup Mobile App (ref-app) - Use Git Bash
```bash
cd ref-app

# Install dependencies (already done)
npm install

# Start Expo development server
npm start
```

## Testing the Setup

### 1. Test Backend API
Open browser and visit:
- Health check: http://localhost:3000/health
- Database test: http://localhost:3000/test/database
- Redis test: http://localhost:3000/test/redis
- S3/MinIO test: http://localhost:3000/test/s3

### 2. Test Mobile App API Connection
1. Start the mobile app (`npm start` in ref-app)
2. Open the app in Expo Go or simulator
3. Navigate to Settings
4. Tap "API Connection Test"
5. Tap "Test All Services" to verify connectivity

### 3. Access MinIO Console
- URL: http://localhost:9001
- Username: minio
- Password: minio123

## Environment Variables
The following are already configured in `ref-api/.env`:
```
DATABASE_URL=******************************************/postgres
REDIS_URL=redis://redis:6379
S3_ENDPOINT=http://minio:9000
S3_ACCESS_KEY=minio
S3_SECRET_KEY=minio123
```

## Development Workflow
1. **Backend Development**: Use WSL terminal in `ref-api/`
2. **Mobile Development**: Use Git Bash terminal in `ref-app/`
3. **Database Changes**: Run `npx prisma db push` after schema changes
4. **API Testing**: Use the mobile app's built-in API test screen

## Troubleshooting

### Backend Issues
- Ensure Docker services are running: `docker compose ps`
- Check API logs: `docker compose logs api`
- Restart API: `npm run start:dev` in ref-api/

### Mobile App Issues
- Clear Expo cache: `npx expo start --clear`
- Ensure backend is running on localhost:3000
- Check network connectivity between mobile device and development machine

### Database Issues
- Reset database: `npx prisma db push --force-reset`
- View database: Use a PostgreSQL client connecting to localhost:5432

## Next Steps
1. ✅ Basic setup complete
2. ✅ API connectivity established
3. 🔄 Add authentication system
4. 🔄 Implement core referee management features
5. 🔄 Add real-time sync capabilities
6. 🔄 Integrate with wearable devices

## Architecture Overview
- **Backend**: NestJS with Prisma ORM, Redis caching, MinIO storage
- **Mobile**: Expo/React Native with TypeScript
- **Database**: PostgreSQL with Prisma migrations
- **Storage**: MinIO (S3-compatible) for file uploads
- **Caching**: Redis for sessions and temporary data
