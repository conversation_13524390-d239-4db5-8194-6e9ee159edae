import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Header from '../components/Header';

const personalInfo = [
  { label: 'First Name', value: 'dave' },
  { label: 'Last Name', value: 'john' },
  { label: 'Weight', value: '(Kg)' },
  { label: 'Height', value: '(Cm)' },
  { label: 'Gender', value: 'Male' },
  { label: 'Change username', value: 'davejohn' },
  { label: 'Change Email address', arrow: true },
];

const refereeDetails = [
  { label: 'Country', arrow: true },
  { label: 'Country FA', arrow: true },
  { label: 'Referee Level', arrow: true },
];

const security = [
  { label: '2FA', arrow: true },
  { label: 'Subscription', arrow: true },
  { label: 'Biometry', arrow: true },
  { label: 'Change password', arrow: true },
  { label: 'App Version', value: 'V 1.2.0' },
  { label: 'Delete Account', arrow: true },
];

type SectionItem = { label: string; value?: string; arrow?: boolean };
type SectionProps = { title: string; items: SectionItem[] };

const Section = ({ title, items }: SectionProps) => (
  <View style={styles.sectionBox}>
    <Text style={styles.sectionTitle}>{title}</Text>
    {items.map((item: SectionItem, idx: number) => (
      <TouchableOpacity
        key={item.label}
        style={[styles.row, idx === items.length - 1 && { borderBottomWidth: 0 }]}
        activeOpacity={item.arrow ? 0.7 : 1}
      >
        <Text style={styles.rowLabel}>{item.label}</Text>
        {item.value && <Text style={styles.rowValue}>{item.value}</Text>}
        {item.arrow && <MaterialIcons name="chevron-right" size={22} color="#222" />}
      </TouchableOpacity>
    ))}
  </View>
);

const Account = () => {
  return (
    <View style={styles.container}>
      <Header title="Account" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.avatarSection}>
          <View style={styles.avatarCircle}>
            <MaterialIcons name="photo-camera" size={40} color="#bbb" />
          </View>
          <Text style={styles.addPicText}>Add Picture</Text>
        </View>
        <Section title="Personal Info" items={personalInfo} />
        <Section title="Referee Details" items={refereeDetails} />
        <Section title="Security" items={security} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    overflow: 'hidden',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  avatarSection: {
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 18,
  },
  avatarCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#f2f2f2',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  addPicText: {
    fontWeight: '600',
    fontSize: 15,
    color: '#222',
    marginBottom: 8,
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    marginBottom: 18,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontWeight: '700',
    fontSize: 14,
    color: '#222',
    backgroundColor: '#fff',
    paddingHorizontal: 14,
    paddingTop: 12,
    paddingBottom: 6,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 13,
    paddingHorizontal: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  rowLabel: {
    fontSize: 15,
    color: '#222',
    fontWeight: '500',
  },
  rowValue: {
    fontSize: 15,
    color: '#888',
    fontWeight: '500',
  },
});

export default Account; 