import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Dimensions } from 'react-native';
import Header from '../components/Header';

const MONTHS = [
  'JAN',
  'FEB',
  'MAR',
  'APR',
  'MAY',
  'JUN',
  'JUL',
  'AUG',
  'SEP',
  'OCT',
  'NOV',
  'DEC',
];

const WEEK_DAYS = ['SAT', 'SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI'];

const generateCalendarGrid = (year: number, month: number) => {
  const firstDayOfMonth = new Date(year, month, 1).getDay();
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  
  let dayCounter = 1;
  const grid = [];
  
  // The week starts on Saturday (index 6), so we adjust
  const startingDayIndex = (firstDayOfMonth + 1) % 7; 

  for (let i = 0; i < 6; i++) {
    const week = [];
    for (let j = 0; j < 7; j++) {
      if ((i === 0 && j < startingDayIndex) || dayCounter > daysInMonth) {
        week.push(null);
      } else {
        week.push(dayCounter++);
      }
    }
    grid.push(week);
    if (dayCounter > daysInMonth) break;
  }
  return grid;
};

const matchesData = [
  '14-05-2025',
  '15-05-2025',
  '18-05-2025',
];

const CalendarScreen = () => {
  const [currentDate, setCurrentDate] = useState(new Date(2023, 5, 1)); // Default to June 2023 to match screenshot
  const [selectedDays, setSelectedDays] = useState<number[]>([2, 3, 4, 5, 11, 17, 18, 20]);
  const monthScrollViewRef = useRef<ScrollView>(null);
  const [monthLayouts, setMonthLayouts] = useState<Record<number, {x: number, width: number}>>({});

  const selectedMonthIndex = currentDate.getMonth();
  const calendarGrid = generateCalendarGrid(currentDate.getFullYear(), selectedMonthIndex);

  useEffect(() => {
    if (monthLayouts[selectedMonthIndex]) {
      const { x, width } = monthLayouts[selectedMonthIndex];
      const screenWidth = Dimensions.get('window').width;
      const scrollToX = x - screenWidth / 2 + width / 2;
      monthScrollViewRef.current?.scrollTo({ x: scrollToX, animated: true });
    }
  }, [selectedMonthIndex, monthLayouts]);

  const handleMonthPress = (monthIndex: number) => {
    setCurrentDate(new Date(currentDate.getFullYear(), monthIndex, 1));
  };

  const handleDayPress = (day: number) => {
    if (selectedDays.includes(day)) {
      setSelectedDays(selectedDays.filter((d) => d !== day));
    } else {
      setSelectedDays([...selectedDays, day]);
    }
  };

  return (
    <View style={styles.container}>
      <Header title="Calendar" />
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        <View style={styles.profileCard}>
          <View style={styles.profilePic} />
          <Text style={styles.profileName}>Dave John</Text>
        </View>

        <ScrollView
          ref={monthScrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.monthScroll}
        >
          {MONTHS.map((month, index) => (
            <TouchableOpacity
              key={month}
              onLayout={(event) => {
                const { x, width } = event.nativeEvent.layout;
                setMonthLayouts((prev) => ({ ...prev, [index]: { x, width } }));
              }}
              style={[
                styles.monthBtn,
                selectedMonthIndex === index && styles.monthBtnActive,
              ]}
              onPress={() => handleMonthPress(index)}
            >
              <Text
                style={[
                  styles.monthText,
                  selectedMonthIndex === index && styles.monthTextActive,
                ]}
              >
                {month}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <View style={styles.calendarGrid}>
          <View style={styles.weekRow}>
            {WEEK_DAYS.map((day) => (
              <View key={day} style={styles.weekDayContainer}>
                <Text style={styles.weekDayText}>{day}</Text>
              </View>
            ))}
          </View>
          {calendarGrid.map((week, weekIndex) => (
            <View key={weekIndex} style={styles.daysRow}>
              {week.map((day, dayIndex) => (
                <TouchableOpacity
                  key={dayIndex}
                  style={[
                    styles.dayBtn,
                    day !== null && selectedDays.includes(day) && styles.dayBtnActive,
                    day === null && styles.dayBtnDisabled,
                  ]}
                  disabled={!day}
                  onPress={() => day && handleDayPress(day)}
                >
                  <Text
                    style={[
                      styles.dayText,
                      day !== null && selectedDays.includes(day) && styles.dayTextActive,
                    ]}
                  >
                    {day}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          ))}
        </View>

        <View style={styles.matchesList}>
          <Text style={styles.matchesTitle}>Matches</Text>
          {matchesData.map((match, index) => (
            <View key={index} style={styles.matchItem}>
              <Text>{match}</Text>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f5f5f5' },
  scrollContent: { paddingHorizontal: 16, paddingTop: 12 }, // Adjusted to match reference margins
  // === MODIFICATION START ===
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 10, // Match reference
    padding: 12, // Match reference
    marginBottom: 16, // Spacing below the card
  },
  profilePic: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#d9d9d9',
    // marginRight removed
  },
  profileName: {
    marginLeft: 16, // Match reference for spacing
    fontSize: 20,
    fontWeight: '700',
    color: '#000000',
  },
  // === MODIFICATION END ===
  monthScroll: { marginBottom: 16 },
  monthBtn: {
    paddingHorizontal: 18,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  monthBtnActive: { backgroundColor: '#066b36', borderWidth: 0 },
  monthText: { color: '#333', fontWeight: '600' },
  monthTextActive: { color: '#fff' },
  calendarGrid: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  weekRow: { 
    flexDirection: 'row', 
    marginBottom: 8 
  },
  weekDayContainer: {
    flex: 1,
    alignItems: 'center',
  },
  weekDayText: {
    color: 'gray',
    fontWeight: '600',
    fontSize: 12,
  },
  daysRow: { flexDirection: 'row', justifyContent: 'space-around', marginBottom: 8 },
  dayBtn: {
    flex: 1,
    margin: 3,
    aspectRatio: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dayBtnActive: { backgroundColor: '#e9f4e8', borderColor: '#a3d3a3' },
  dayBtnDisabled: { backgroundColor: 'transparent', borderWidth: 0 },
  dayText: { color: '#000', fontWeight: 'bold', fontSize: 16 },
  dayTextActive: { color: '#066b36', fontWeight: 'bold' },
  matchesList: { marginTop: 8 },
  matchesTitle: { fontWeight: 'bold', fontSize: 16, marginBottom: 8 },
  matchItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
});

export default CalendarScreen;