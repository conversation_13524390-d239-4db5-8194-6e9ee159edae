import React from 'react';
import { View, Text, ScrollView, StyleSheet, Linking } from 'react-native';
import Header from '../components/Header';

const PrivacyPolicyScreen = () => {
  const currentYear = new Date().getFullYear();

  const handleOpenLink = (url: string) => {
    Linking.openURL(url);
  };

  return (
    <View style={styles.container}>
      <Header title="Privacy Policy" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.sectionBox}>
          <Text style={styles.lastUpdated}>Last updated: June 17, 2025</Text>
          
          <Text style={styles.paragraph}>
            At Referee Tracker, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application and services (collectively, the "Service").
          </Text>
          
          <Text style={styles.sectionTitle}>1. Information We Collect</Text>
          <Text style={styles.subsectionTitle}>Personal Information</Text>
          <Text style={styles.paragraph}>
            We may collect personal information that you provide to us, such as:
          </Text>
          <Text style={styles.listItem}>• Name and contact information (email, phone number)</Text>
          <Text style={styles.listItem}>• Account credentials</Text>
          <Text style={styles.listItem}>• Payment information</Text>
          
          <Text style={styles.subsectionTitle}>Usage Data</Text>
          <Text style={styles.paragraph}>
            We may collect information about how you access and use the Service, including:
          </Text>
          <Text style={styles.listItem}>• Device information (model, OS version, unique device identifiers)</Text>
          <Text style={styles.listItem}>• Log data (IP address, browser type, pages visited)</Text>
          <Text style={styles.listItem}>• Usage patterns and preferences</Text>
          
          <Text style={styles.sectionTitle}>2. How We Use Your Information</Text>
          <Text style={styles.paragraph}>
            We may use the information we collect for various purposes, including to:
          </Text>
          <Text style={styles.listItem}>• Provide, maintain, and improve our Service</Text>
          <Text style={styles.listItem}>• Process transactions and send related information</Text>
          <Text style={styles.listItem}>• Respond to your comments, questions, and requests</Text>
          <Text style={styles.listItem}>• Send you technical notices and support messages</Text>
          <Text style={styles.listItem}>• Detect, investigate, and prevent fraudulent activities</Text>
          
          <Text style={styles.sectionTitle}>3. How We Share Your Information</Text>
          <Text style={styles.paragraph}>
            We may share your information in the following situations:
          </Text>
          <Text style={styles.listItem}>• With service providers who perform services on our behalf</Text>
          <Text style={styles.listItem}>• To comply with legal obligations</Text>
          <Text style={styles.listItem}>• To protect and defend our rights and property</Text>
          <Text style={styles.listItem}>• With your consent or at your direction</Text>
          
          <Text style={styles.sectionTitle}>4. Data Security</Text>
          <Text style={styles.paragraph}>
            We implement appropriate technical and organizational measures to protect your personal information. However, no method of transmission over the Internet or electronic storage is 100% secure.
          </Text>
          
          <Text style={styles.sectionTitle}>5. Your Privacy Rights</Text>
          <Text style={styles.paragraph}>
            Depending on your location, you may have certain rights regarding your personal information, including:
          </Text>
          <Text style={styles.listItem}>• The right to access and receive a copy of your information</Text>
          <Text style={styles.listItem}>• The right to rectify any information that is inaccurate</Text>
          <Text style={styles.listItem}>• The right to request deletion of your information</Text>
          <Text style={styles.listItem}>• The right to object to or restrict processing of your information</Text>
          
          <Text style={styles.sectionTitle}>6. Children's Privacy</Text>
          <Text style={styles.paragraph}>
            Our Service is not intended for children under 13. We do not knowingly collect personal information from children under 13.
          </Text>
          
          <Text style={styles.sectionTitle}>7. Changes to This Policy</Text>
          <Text style={styles.paragraph}>
            We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.
          </Text>
          
          <Text style={styles.sectionTitle}>8. Contact Us</Text>
          <Text style={[styles.paragraph, styles.noMarginBottom]}>
            If you have any questions about this Privacy Policy, please contact us at:
          </Text>
          <Text 
            style={styles.link}
            onPress={() => handleOpenLink('mailto:<EMAIL>')}
          >
            <EMAIL>
          </Text>
          
          <Text style={[styles.paragraph, styles.centered, styles.marginTop]}>
            © {currentYear} Referee Tracker. All rights reserved.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    overflow: 'hidden',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    padding: 16,
    marginTop: 8,
  },
  lastUpdated: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
    marginBottom: 16,
    textAlign: 'right',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#222',
    marginTop: 24,
    marginBottom: 8,
  },
  subsectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginTop: 12,
    marginBottom: 6,
  },
  paragraph: {
    fontSize: 14,
    lineHeight: 22,
    color: '#444',
    marginBottom: 12,
  },
  listItem: {
    fontSize: 14,
    lineHeight: 22,
    color: '#444',
    marginLeft: 16,
    marginBottom: 4,
  },
  link: {
    color: '#066b36',
    textDecorationLine: 'underline',
    marginTop: 4,
    fontSize: 14,
  },
  noMarginBottom: {
    marginBottom: 0,
  },
  centered: {
    textAlign: 'center',
  },
  marginTop: {
    marginTop: 24,
  },
});

export default PrivacyPolicyScreen;
