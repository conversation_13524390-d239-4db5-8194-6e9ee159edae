import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Header from '../components/Header';

const faqItems = [
  { 
    question: 'How do I reset my password?',
    answer: 'To reset your password, go to the login screen and tap on "Forgot Password". Follow the instructions sent to your email.'
  },
  { 
    question: 'How do I update my profile information?',
    answer: 'You can update your profile information by going to the Settings screen and selecting "Edit Profile".'
  },
  { 
    question: 'How do I contact support?',
    answer: 'You can contact our support team through the Contact Us section in the Support screen.'
  },
  { 
    question: 'Is there a mobile app available?',
    answer: 'Yes, our mobile app is available for both iOS and Android devices.'
  },
  { 
    question: 'How do I delete my account?',
    answer: 'To delete your account, go to Settings > Account > Delete Account. Please note this action cannot be undone.'
  },
];

const FAQScreen = () => {
  const [expandedItem, setExpandedItem] = React.useState<number | null>(null);

  const toggleItem = (index: number) => {
    setExpandedItem(expandedItem === index ? null : index);
  };

  return (
    <View style={styles.container}>
      <Header title="FAQ" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.sectionBox}>
          {faqItems.map((item, index) => (
            <View key={index}>
              <TouchableOpacity
                style={[styles.row, index === faqItems.length - 1 && expandedItem !== index && { borderBottomWidth: 0 }]}
                onPress={() => toggleItem(index)}
                activeOpacity={0.7}
              >
                <Text style={styles.questionText}>{item.question}</Text>
                <MaterialIcons 
                  name={expandedItem === index ? 'keyboard-arrow-up' : 'keyboard-arrow-down'} 
                  size={24} 
                  color="#666" 
                />
              </TouchableOpacity>
              {expandedItem === index && (
                <View style={styles.answerContainer}>
                  <Text style={styles.answerText}>{item.answer}</Text>
                </View>
              )}
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    overflow: 'hidden',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    marginTop: 8,
    overflow: 'hidden',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  questionText: {
    flex: 1,
    fontSize: 15,
    color: '#222',
    fontWeight: '500',
    marginRight: 12,
  },
  answerContainer: {
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  answerText: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
});

export default FAQScreen;
