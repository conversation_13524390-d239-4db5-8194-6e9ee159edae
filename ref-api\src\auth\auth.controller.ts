import { Controller, Post, Body, HttpException, HttpStatus } from '@nestjs/common';
import { AuthService } from './auth.service';

export class AuthTokenDto {
  idToken: string;
}

export class AuthResponseDto {
  success: boolean;
  user?: any;
  message?: string;
  error?: string;
}

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('verify-token')
  async verifyToken(@Body() authTokenDto: AuthTokenDto): Promise<AuthResponseDto> {
    try {
      const { idToken } = authTokenDto;
      
      if (!idToken) {
        throw new HttpException('ID token is required', HttpStatus.BAD_REQUEST);
      }

      const user = await this.authService.verifyAndCreateUser(idToken);
      
      return {
        success: true,
        user,
        message: 'Authentication successful'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Authentication failed'
      };
    }
  }

  @Post('user-info')
  async getUserInfo(@Body() body: { firebaseUid: string }): Promise<AuthResponseDto> {
    try {
      const { firebaseUid } = body;
      
      if (!firebaseUid) {
        throw new HttpException('Firebase UID is required', HttpStatus.BAD_REQUEST);
      }

      const user = await this.authService.getUserByFirebaseUid(firebaseUid);
      
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      return {
        success: true,
        user,
        message: 'User found'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to get user info'
      };
    }
  }
}
