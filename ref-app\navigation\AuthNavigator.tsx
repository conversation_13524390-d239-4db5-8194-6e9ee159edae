import React from 'react';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { useAuth } from '../contexts/AuthContext';

// Import screens
import LoginScreen from '../screens/LoginScreen';
import ProfileSetup from '../screens/ProfileSetup';
import ConnectWatch from '../screens/ConnectWatch';
import Dashboard from '../screens/Dashboard';
import Evaluate from '../screens/Evaluate';
import Account from '../screens/Account';
import Settings from '../screens/Settings';
import Integrations from '../screens/Integrations';
import CreateMatchScreen from '../screens/CreateMatchScreen';
import AppTutorialScreen from '../screens/AppTutorialScreen';
import GoProScreen from '../screens/GoProScreen';
import SupportScreen from '../screens/SupportScreen';
import FAQScreen from '../screens/FAQScreen';
import FeatureRequestsScreen from '../screens/FeatureRequestsScreen';
import WatchDebuggerScreen from '../screens/WatchDebuggerScreen';
import ContactUsScreen from '../screens/ContactUsScreen';
import TermsConditionsScreen from '../screens/TermsConditionsScreen';
import PrivacyPolicyScreen from '../screens/PrivacyPolicyScreen';
import FitnessSyncScreen from '../screens/FitnessSyncScreen';
import WhyUsScreen from '../screens/WhyUsScreen';

const Stack = createStackNavigator();

const AuthNavigator: React.FC = () => {
  const { user, loading } = useAuth();

  // Show loading screen while checking auth state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {user ? (
        // User is signed in - show app screens
        <>
          <Stack.Screen name="Dashboard" component={Dashboard} />
          <Stack.Screen name="ProfileSetup" component={ProfileSetup} />
          <Stack.Screen name="ConnectWatch" component={ConnectWatch} />
          <Stack.Screen name="Account" component={Account} />
          <Stack.Screen name="Settings" component={Settings} />
          <Stack.Screen name="Integrations" component={Integrations} />
          <Stack.Screen name="Evaluate" component={Evaluate} />
          <Stack.Screen 
            name="CreateMatch" 
            component={CreateMatchScreen} 
            options={{
              presentation: 'modal',
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="AppTutorial" 
            component={AppTutorialScreen} 
            options={{
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="GoPro" 
            component={GoProScreen} 
            options={{
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="Support" 
            component={SupportScreen} 
            options={{
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="FAQ" 
            component={FAQScreen} 
            options={{
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="FeatureRequests" 
            component={FeatureRequestsScreen} 
            options={{
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="WatchDebugger" 
            component={WatchDebuggerScreen} 
            options={{
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="ContactUs" 
            component={ContactUsScreen} 
            options={{
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="TermsConditions" 
            component={TermsConditionsScreen} 
            options={{
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="PrivacyPolicy" 
            component={PrivacyPolicyScreen} 
            options={{
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="FitnessSync" 
            component={FitnessSyncScreen} 
            options={{
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen
            name="WhyUs"
            component={WhyUsScreen}
            options={{
              cardStyle: { backgroundColor: 'white' }
            }}
          />

        </>
      ) : (
        // User is not signed in - show auth screens
        <Stack.Screen name="Login" component={LoginScreen} />
      )}
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});

export default AuthNavigator;
