{"expo": {"name": "mobile-app", "slug": "mobile-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "android": {"package": "com.refrate.refereetracker", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "ios": {"bundleIdentifier": "com.refrate.refereetracker", "supportsTablet": true}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "fc356406-fa6c-49f1-b944-4cfb129a9489"}}, "plugins": ["expo-web-browser", ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.723844449983-your-ios-client-id"}]]}}