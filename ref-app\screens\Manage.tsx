import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons, MaterialCommunityIcons, FontAwesome5, Ionicons, Feather } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import Header from '../components/Header';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },

  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
    paddingTop: 16,
  },
  sectionHeader: {
    fontWeight: '700',
    fontSize: 15,
    color: '#222',
    marginTop: 10,
    marginBottom: 8,
    marginLeft: 4,
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#222',
    marginBottom: 18,
    overflow: 'hidden',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  rowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowLabel: {
    fontSize: 15,
    color: '#222',
    marginLeft: 14,
    fontWeight: '500',
  },
  goProLabel: {
    color: '#066b36',
    fontWeight: 'bold',
  },
  boldLabel: {
    fontWeight: 'bold',
  },
  logoutRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginTop: 18,
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#222',
    paddingVertical: 14,
    paddingHorizontal: 14,
  },
  logoutLabel: {
    fontSize: 15,
    color: '#d32f2f',
    marginLeft: 14,
    fontWeight: '600',
  },
});

const profileItems = [
  { icon: <MaterialIcons name="person-outline" size={22} color="#222" />, label: 'Account', nav: 'Account' },
  { icon: <MaterialIcons name="settings" size={22} color="#222" />, label: 'Settings', nav: 'Settings' },
  { icon: <MaterialCommunityIcons name="puzzle-outline" size={22} color="#222" />, label: 'Integrations', nav: 'Integrations' },
  { 
    icon: <MaterialCommunityIcons name="medal-outline" size={22} color="#066b36" />, 
    label: 'Go Pro Now', 
    labelStyle: styles.goProLabel,
    nav: 'GoPro'
  },
];

const helpItems = [
  { 
    icon: <MaterialIcons name="help-outline" size={22} color="#222" />, 
    label: 'Support',
    nav: 'Support'
  },
  { 
    icon: <MaterialCommunityIcons name="run-fast" size={22} color="#222" />, 
    label: 'App Tutorial',
    nav: 'AppTutorial'
  },
  { 
    icon: <MaterialCommunityIcons name="dumbbell" size={22} color="#222" />, 
    label: 'Fitness Sync',
    nav: 'FitnessSync'
  },
  { 
    icon: <MaterialCommunityIcons name="star-circle-outline" size={22} color="#222" />, 
    label: 'Why Us',
    nav: 'WhyUs'
  },
  { 
    icon: <Feather name="share-2" size={22} color="#222" />, 
    label: 'Share this app',
    onPress: () => { /* Share functionality */ }
  },
  { 
    icon: <MaterialIcons name="android" size={22} color="#222" />, 
    label: 'Rate us on Google Play', 
    labelStyle: styles.boldLabel,
    onPress: () => { /* Open Play Store */ }
  },
  { 
    icon: <Ionicons name="logo-apple" size={22} color="#222" />, 
    label: 'Rate us on Apple Store', 
    labelStyle: styles.boldLabel,
    onPress: () => { /* Open App Store */ }
  },
];

const Manage = () => {
  const navigation = useNavigation<StackNavigationProp<any>>();
  
  const renderItem = (item: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.row}
      onPress={() => {
        if (item.nav) {
          navigation.navigate(item.nav as never);
        } else if (item.onPress) {
          item.onPress();
        }
      }}
    >
      <View style={styles.rowLeft}>
        {item.icon}
        <Text style={[styles.rowLabel, item.labelStyle]}>{item.label}</Text>
      </View>
      <MaterialIcons name="chevron-right" size={22} color="#888" />
    </TouchableOpacity>
  );
  
  return (
    <View style={styles.container}>
      <Header title="Manage" />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionHeader}>Profile</Text>
        <View style={styles.sectionBox}>
          {profileItems.map((item, idx) => renderItem(item, idx))}
        </View>
        <Text style={styles.sectionHeader}>More Options</Text>
        <View style={styles.sectionBox}>
          {helpItems.map((item, idx) => renderItem(item, idx))}
        </View>
        <TouchableOpacity style={styles.logoutRow} activeOpacity={0.7}>
          <MaterialIcons name="power-settings-new" size={22} color="#d32f2f" />
          <Text style={styles.logoutLabel}>Logout</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default Manage; 