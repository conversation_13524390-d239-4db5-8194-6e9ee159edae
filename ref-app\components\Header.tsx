import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  rightComponent?: React.ReactNode;
  onBackPress?: () => void;
}

const { width } = Dimensions.get('window');
const FONT_SCALE = width / 400;

const Header: React.FC<HeaderProps> = ({ title, showBackButton, rightComponent, onBackPress }) => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  
  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
      <View style={styles.sideContainer}>
        {showBackButton && (
          <TouchableOpacity style={styles.backBtn} onPress={handleBackPress}>
            <MaterialIcons name="arrow-back-ios" size={22 * FONT_SCALE} color="#066b36" />
          </TouchableOpacity>
        )}
      </View>
      <View style={styles.titleContainer}>
        <Text style={[styles.headerTitle, { fontSize: 17 * FONT_SCALE }]}>{title}</Text>
      </View>
      <View style={styles.sideContainer}>
        {rightComponent}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 10,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  sideContainer: {
    width: 50,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  backBtn: {
    padding: 5,
  },
  headerTitle: {
    fontWeight: '700',
    color: '#111',
  },
});

export default Header;
