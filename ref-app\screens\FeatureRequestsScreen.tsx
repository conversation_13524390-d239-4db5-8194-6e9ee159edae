import React, { useState } from 'react';
import { View, Text, ScrollView, TextInput, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import Header from '../components/Header';

const FeatureRequestsScreen = () => {
  const [request, setRequest] = useState('');
  const [email, setEmail] = useState('');

  const handleSubmit = () => {
    if (!request.trim()) {
      Alert.alert('Error', 'Please enter your feature request');
      return;
    }
    // Here you would typically send the request to your backend
    Alert.alert('Thank You', 'Your feature request has been submitted successfully!');
    setRequest('');
    setEmail('');
  };

  return (
    <View style={styles.container}>
      <Header title="Feature Requests" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.sectionBox}>
          <Text style={styles.description}>
            We'd love to hear your suggestions for new features or improvements. Let us know what you'd like to see in future updates!
          </Text>
          
          <Text style={styles.label}>Your Feature Request</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Describe the feature you'd like to see..."
            placeholderTextColor="#999"
            multiline
            numberOfLines={5}
            value={request}
            onChangeText={setRequest}
          />
          
          <Text style={styles.label}>Your Email (Optional)</Text>
          <TextInput
            style={styles.input}
            placeholder="<EMAIL>"
            placeholderTextColor="#999"
            keyboardType="email-address"
            autoCapitalize="none"
            value={email}
            onChangeText={setEmail}
          />
          
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <Text style={styles.submitButtonText}>Submit Request</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    overflow: 'hidden',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    padding: 16,
    marginTop: 8,
  },
  description: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 12,
  },
  input: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    fontSize: 15,
    color: '#222',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#066b36',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default FeatureRequestsScreen;
