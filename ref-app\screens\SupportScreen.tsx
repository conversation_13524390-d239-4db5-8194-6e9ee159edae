import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import Header from '../components/Header';

type SectionItem = { 
  label: string; 
  value?: string; 
  arrow?: boolean; 
  onPress?: () => void;
};

type SectionProps = { 
  title?: string; 
  items: SectionItem[] 
};

const Section = ({ title, items }: SectionProps) => (
  <View style={styles.sectionBox}>
    {title && <Text style={styles.sectionTitle}>{title}</Text>}
    {items.map((item: SectionItem, idx: number) => (
      <TouchableOpacity
        key={item.label}
        style={[styles.row, idx === items.length - 1 && { borderBottomWidth: 0 }]}
        activeOpacity={0.7}
        onPress={item.onPress}
      >
        <Text style={styles.rowLabel}>{item.label}</Text>
        <View style={styles.rowRight}>
          {item.value && <Text style={styles.rowValue}>{item.value}</Text>}
          {item.arrow && <MaterialIcons name="chevron-right" size={22} color="#222" />}
        </View>
      </TouchableOpacity>
    ))}
  </View>
);

const SupportScreen = () => {
  const navigation = useNavigation();

  const supportItems = [
    { 
      label: 'Frequently Asked Questions', 
      arrow: true,
      onPress: () => navigation.navigate('FAQ' as never)
    },
    { 
      label: 'Feature Requests', 
      arrow: true,
      onPress: () => navigation.navigate('FeatureRequests' as never)
    },
    { 
      label: 'Watch Debugger', 
      arrow: true,
      onPress: () => navigation.navigate('WatchDebugger' as never)
    },
    { 
      label: 'Contact us', 
      arrow: true,
      onPress: () => navigation.navigate('ContactUs' as never)
    },
    { 
      label: 'Terms and conditions', 
      arrow: true,
      onPress: () => navigation.navigate('TermsConditions' as never)
    },
    { 
      label: 'Privacy Policy', 
      arrow: true,
      onPress: () => navigation.navigate('PrivacyPolicy' as never)
    },
  ];

  return (
    <View style={styles.container}>
      <Header title="Support" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <Section items={supportItems} />
      </ScrollView>
    </View>
  );
};

export default SupportScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    overflow: 'hidden',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    marginBottom: 18,
    marginTop: 8,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontWeight: '700',
    fontSize: 14,
    color: '#222',
    backgroundColor: '#fff',
    paddingHorizontal: 14,
    paddingTop: 12,
    paddingBottom: 6,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 13,
    paddingHorizontal: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  rowLabel: {
    fontSize: 15,
    color: '#222',
    fontWeight: '500',
    flex: 1,
  },
  rowRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowValue: {
    fontSize: 15,
    color: '#888',
    fontWeight: '500',
    marginRight: 8,
  },
});