import React from 'react';
import { View, StyleSheet, ScrollView, Text } from 'react-native';
import Header from '../components/Header';

const FitnessSyncScreen = () => {
  return (
    <View style={styles.container}>
      <Header title="Fitness Sync" showBackButton={true} />
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Fitness Sync</Text>
        <Text style={styles.text}>
          Connect your fitness devices and apps to sync your activity data.
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
    marginTop: 8,
  },
  text: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
});

export default FitnessSyncScreen;
