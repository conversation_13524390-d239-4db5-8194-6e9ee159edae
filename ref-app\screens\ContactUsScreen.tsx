import React, { useState } from 'react';
import { View, Text, ScrollView, TextInput, TouchableOpacity, StyleSheet, Alert, Linking, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Header from '../components/Header';
import { useNavigation } from '@react-navigation/native';

const ContactUsScreen = ({ navigation }: any) => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = () => {
    if (!name.trim() || !email.trim() || !message.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }
    
    if (!/\S+@\S+\.\S+/.test(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      Alert.alert('Thank You', 'Your message has been sent successfully! We\'ll get back to you soon.');
      setName('');
      setEmail('');
      setMessage('');
    }, 1500);
  };

  const handleEmailPress = () => {
    Linking.openURL('mailto:<EMAIL>');
  };

  const handleCallPress = () => {
    Linking.openURL('tel:+1234567890');
  };

  return (
    <View style={styles.container}>
      <Header title="Contact Us" showBackButton={true} onBackPress={() => navigation.goBack()} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.sectionBox}>
          <Text style={styles.sectionTitle}>Send us a Message</Text>
          
          <Text style={styles.label}>Full Name</Text>
          <TextInput
            style={styles.input}
            placeholder="John Doe"
            placeholderTextColor="#999"
            value={name}
            onChangeText={setName}
          />
          
          <Text style={styles.label}>Email Address</Text>
          <TextInput
            style={styles.input}
            placeholder="<EMAIL>"
            placeholderTextColor="#999"
            keyboardType="email-address"
            autoCapitalize="none"
            value={email}
            onChangeText={setEmail}
          />
          
          <Text style={styles.label}>Your Message</Text>
          <TextInput
            style={[styles.input, styles.messageInput]}
            placeholder="How can we help you?"
            placeholderTextColor="#999"
            multiline
            numberOfLines={5}
            value={message}
            onChangeText={setMessage}
          />
          
          <TouchableOpacity 
            style={styles.submitButton} 
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.submitButtonText}>Send Message</Text>
            )}
          </TouchableOpacity>
        </View>
        
        <View style={styles.sectionBox}>
          <Text style={styles.sectionTitle}>Other Ways to Reach Us</Text>
          
          <TouchableOpacity 
            style={styles.contactMethod} 
            onPress={handleEmailPress}
          >
            <View style={[styles.contactIcon, { backgroundColor: '#2196F3' }]}>
              <MaterialIcons name="email" size={20} color="#fff" />
            </View>
            <View style={styles.contactTextContainer}>
              <Text style={styles.contactLabel}>Email Us</Text>
              <Text style={styles.contactValue}><EMAIL></Text>
            </View>
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          </TouchableOpacity>
          
          <View style={styles.divider} />
          
          <TouchableOpacity 
            style={styles.contactMethod}
            onPress={handleCallPress}
          >
            <View style={[styles.contactIcon, { backgroundColor: '#4CAF50' }]}>
              <MaterialIcons name="phone" size={20} color="#fff" />
            </View>
            <View style={styles.contactTextContainer}>
              <Text style={styles.contactLabel}>Call Us</Text>
              <Text style={styles.contactValue}>+1 (234) 567-890</Text>
            </View>
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          </TouchableOpacity>
          
          <View style={styles.divider} />
          
          <View style={styles.contactMethod}>
            <View style={[styles.contactIcon, { backgroundColor: '#FF9800' }]}>
              <MaterialIcons name="location-on" size={20} color="#fff" />
            </View>
            <View style={styles.contactTextContainer}>
              <Text style={styles.contactLabel}>Visit Us</Text>
              <Text style={styles.contactValue}>123 Referee Street, Sports City, 10001</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    overflow: 'hidden',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    padding: 16,
    marginTop: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#222',
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#555',
    fontWeight: '600',
    marginBottom: 6,
    marginTop: 12,
  },
  input: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    fontSize: 15,
    color: '#222',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  messageInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#066b36',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  contactMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contactTextContainer: {
    flex: 1,
  },
  contactLabel: {
    fontSize: 15,
    color: '#222',
    fontWeight: '500',
    marginBottom: 2,
  },
  contactValue: {
    fontSize: 13,
    color: '#666',
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginLeft: 52,
  },
});

export default ContactUsScreen;
