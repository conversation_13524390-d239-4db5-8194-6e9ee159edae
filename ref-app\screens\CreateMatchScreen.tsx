import React from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import Header from '../components/Header';

const CreateMatchScreen = () => {
  return (
    <SafeAreaView style={styles.container}>
      <Header title="Create Match" showBackButton={true} />
      <View style={styles.content}>
        {/* Content will be added later */}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    padding: 16,
  },
});

export default CreateMatchScreen;
