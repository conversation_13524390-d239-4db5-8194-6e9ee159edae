import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import Header from '../components/Header';

const { width } = Dimensions.get('window');

// Match data for upcoming matches
const upcomingMatches = [
  {
    division: "East Gwent Division",
    role: "Referee",
    homeTeam: {
      name: "Monmouth Town FC Reserves",
      kitColor: "#0066cc",
    },
    awayTeam: {
      name: "Chepstow Town FC 3rd Team",
      kitColor: "#cc0000",
    },
    location: "Manhattan City",
    date: "05.25.25",
    time: "19:35",
  },
  {
    division: "East Gwent Division",
    role: "Ar2",
    homeTeam: {
      name: "Monmouth Town FC Reserves",
      kitColor: "#9933cc",
    },
    awayTeam: {
      name: "Chepstow Town FC 3rd Team",
      kitColor: "#66cc00",
    },
    location: "Manhattan City",
    date: "05.25.25",
    time: "19:35",
  },
];

type Match = {
  division: string;
  role: string;
  homeTeam: { name: string; kitColor: string };
  awayTeam: { name: string; kitColor: string };
  location: string;
  date: string;
  time: string;
};

const UserDashboard = () => {
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState('upcoming');



  const renderUserProfile = () => (
    <View style={styles.profileCard}>
      <View style={styles.avatar} />
      <Text style={styles.userName}>Dave John</Text>
    </View>
  );

  const renderTabs = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'upcoming' && styles.tabActive
        ]}
        onPress={() => setActiveTab('upcoming')}
      >
        <Text style={[
          styles.tabText,
          activeTab === 'upcoming' && styles.tabTextActive
        ]}>
          Upcoming
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'previous' && styles.tabInactive
        ]}
        onPress={() => setActiveTab('previous')}
      >
        <Text style={[
          styles.tabText,
          activeTab === 'previous' && styles.tabTextInactive
        ]}>
          Previous
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderCountdown = () => (
    <View style={styles.countdownSection}>
      <Text style={styles.nextGameText}>Next Game FC v FC1</Text>
      <View style={styles.countdownCard}>
        <View style={styles.countdownItem}>
          <Text style={styles.countdownNumber}>5</Text>
          <Text style={styles.countdownLabel}>Days</Text>
        </View>
        <View style={styles.separator} />
        <View style={styles.countdownItem}>
          <Text style={styles.countdownNumber}>21</Text>
          <Text style={styles.countdownLabel}>Hours</Text>
        </View>
        <View style={styles.separator} />
        <View style={styles.countdownItem}>
          <Text style={styles.countdownNumber}>30</Text>
          <Text style={styles.countdownLabel}>Mins</Text>
        </View>
      </View>
    </View>
  );

  const renderKitIcon = (color: string) => (
    <View style={styles.kitIconContainer}>
      {/* Shirt */}
      <MaterialIcons name="person" size={18} color={color} />
      {/* Shorts - using a smaller icon positioned below */}
      <View style={[styles.shortsIcon, { backgroundColor: color }]} />
    </View>
  );

  const renderMatchCard = (match: Match, index: number) => (
    <View key={index} style={styles.matchCard}>
      <View style={styles.matchHeader}>
        <View style={styles.matchDivision}>
          <View style={styles.divisionDot} />
          <Text style={styles.divisionText}>{match.division}</Text>
        </View>
        <View style={styles.roleBadge}>
          <Text style={styles.roleText}>{match.role}</Text>
        </View>
      </View>

      <View style={styles.teamsCard}>
        <View style={styles.teamSection}>
          {renderKitIcon(match.homeTeam.kitColor)}
          <Text style={styles.teamName}>{match.homeTeam.name}</Text>
        </View>
        
        <View style={styles.teamSeparator} />
        
        <View style={styles.teamSection}>
          {renderKitIcon(match.awayTeam.kitColor)}
          <Text style={styles.teamName}>{match.awayTeam.name}</Text>
        </View>
      </View>

      <View style={styles.matchDetails}>
        <View style={styles.locationSection}>
          <MaterialIcons name="location-on" size={16} color="#666" />
          <Text style={styles.locationText}>{match.location}</Text>
        </View>
        <View style={styles.timeSection}>
          <Text style={styles.dateText}>{match.date}</Text>
          <View style={styles.timeSeparator} />
          <Text style={styles.timeText}>{match.time}</Text>
        </View>
      </View>
    </View>
  );

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Header 
        title="Dashboard" 
        rightComponent={
          <TouchableOpacity 
            onPress={() => navigation.navigate('CreateMatch' as never)}
            style={styles.addButton}
          >
            <MaterialIcons name="add" size={24} color="#066b36" />
          </TouchableOpacity>
        }
      />
    </View>
  );

  return (
    <View style={styles.container}>
      {renderHeader()}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderUserProfile()}
        {renderTabs()}
        
        {activeTab === 'upcoming' && (
          <>
            {renderCountdown()}
            {upcomingMatches.map(renderMatchCard)}
          </>
        )}
        
        {activeTab === 'previous' && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>Previous matches will appear here</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    backgroundColor: '#fff',
  },
  addButton: {
    padding: 3.9,
    marginLeft: 15, // pushes the icon slightly to the right
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },

  content: {
    flex: 1,
  },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginTop: 12,
    marginBottom: 8,
    padding: 12,
    borderRadius: 10,
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#d9d9d9',
  },
  userName: {
    marginLeft: 16,
    fontSize: 20,
    fontWeight: '700',
    color: '#000000',
  },
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginVertical: 12,
    height: 40,
    borderRadius: 6,
    overflow: 'hidden',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabActive: {
    backgroundColor: '#066b36',
  },
  tabInactive: {
    backgroundColor: '#e5e5e5',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
  },
  tabTextActive: {
    color: '#ffffff',
  },
  tabTextInactive: {
    color: '#666666',
  },
  countdownSection: {
    alignItems: 'center',
    marginTop: 4,
    marginBottom: 16,
  },
  nextGameText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  countdownCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    width: width * 0.75,
    justifyContent: 'space-evenly',
  },
  countdownItem: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 50,
  },
  countdownNumber: {
    fontSize: 28,
    fontWeight: '700',
    color: '#000000',
    lineHeight: 32,
  },
  countdownLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
    marginTop: 2,
  },
  separator: {
    width: 1,
    height: 30,
    backgroundColor: '#e0e0e0',
  },
  matchCard: {
    marginHorizontal: 16,
    marginBottom: 12,
    padding: 12,
    backgroundColor: '#ffffff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  matchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  matchDivision: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  divisionDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#066b36',
  },
  divisionText: {
    marginLeft: 8,
    fontSize: 13,
    fontWeight: '500',
    color: '#666666',
  },
  roleBadge: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  roleText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#333333',
  },
  teamsCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    marginBottom: 8,
  },
  teamSection: {
    alignItems: 'center',
    flex: 1,
  },
  kitIconContainer: {
    alignItems: 'center',
    marginBottom: 6,
    position: 'relative',
  },
  shortsIcon: {
    width: 12,
    height: 6,
    borderRadius: 3,
    marginTop: -2,
  },
  teamName: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333333',
    textAlign: 'center',
    lineHeight: 16,
  },
  teamSeparator: {
    width: 1,
    height: 32,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 12,
  },
  matchDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  locationSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  locationText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  timeSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  timeSeparator: {
    width: 1,
    height: 16,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 12,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
  },
});

export default UserDashboard;