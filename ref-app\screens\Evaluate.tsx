import React, { useRef, useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  ScrollView, 
  Dimensions
} from 'react-native';
import Header from '../components/Header';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

type TabType = {
  key: string;
  label: string;
};

const TABS: TabType[] = [
  { key: 'matches', label: 'Matches' },
  { key: 'misconduct', label: 'Misconduct' },
  { key: 'positioning', label: 'Positioning' },
  { key: 'distance', label: 'Distance' },
  { key: 'speed', label: 'Speed' },
  { key: 'health', label: 'Health' },
];

const Evaluate = () => {
  const [activeTab, setActiveTab] = useState<string>(TABS[0].key);
  const scrollViewRef = useRef<ScrollView>(null);
  const [tabLayouts, setTabLayouts] = useState<Record<number, {x: number, width: number}>>({});

  const activeTabIndex = TABS.findIndex(tab => tab.key === activeTab);

  useEffect(() => {
    if (tabLayouts[activeTabIndex]) {
      const { x, width } = tabLayouts[activeTabIndex];
      const scrollToX = x - SCREEN_WIDTH / 2 + width / 2;
      scrollViewRef.current?.scrollTo({ x: scrollToX, animated: true });
    }
  }, [activeTabIndex, tabLayouts]);

  const handleTabPress = (tab: TabType, index: number) => {
    setActiveTab(tab.key);
  };

  return (
    <View style={styles.container}>
      <Header title="Evaluate" showBackButton={false} />

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsScrollContainer}
          bounces={false}
        >
          {TABS.map((tab, index) => (
            <TouchableOpacity
              key={tab.key}
              onLayout={(event) => {
                const { x, width } = event.nativeEvent.layout;
                setTabLayouts((prev) => ({ ...prev, [index]: { x, width } }));
              }}
              style={[
                styles.tab,
                activeTab === tab.key && styles.tabActive,
              ]}
              onPress={() => handleTabPress(tab, index)}
              activeOpacity={0.8}
            >
              <Text 
                style={[
                  styles.tabText,
                  activeTab === tab.key && styles.tabTextActive,
                ]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Placeholder for tab content */}
      <View style={styles.contentPlaceholder}>
        <Text style={styles.contentText}>Content for {activeTab}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  tabsContainer: {
    backgroundColor: '#f5f5f5',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  tabsScrollContainer: {
    paddingHorizontal: 0,
  },
  tab: {
    paddingHorizontal: 18,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabActive: {
    backgroundColor: '#066b36',
    borderWidth: 0,
  },
  tabText: {
    fontSize: 15,
    fontWeight: '600',
    textAlign: 'center',
    color: '#333',
  },
  tabTextActive: {
    color: '#fff',
    fontWeight: '600',
  },
  contentPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  contentText: {
    color: '#666',
    fontSize: 16,
  },
});
export default Evaluate;