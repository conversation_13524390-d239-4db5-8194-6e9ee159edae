import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { FirebaseService } from './firebase.service';

export interface AuthUser {
  id: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  emailVerified: boolean;
  provider: string;
  firebaseUid: string;
}

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private firebaseService: FirebaseService,
  ) {}

  async verifyAndCreateUser(idToken: string): Promise<AuthUser> {
    try {
      // Verify the Firebase ID token
      const decodedToken = await this.firebaseService.verifyIdToken(idToken);
      
      // Extract user information from the token
      const {
        uid: firebaseUid,
        email,
        name: displayName,
        picture: photoURL,
        email_verified: emailVerified,
        firebase: { sign_in_provider: provider }
      } = decodedToken;

      // Check if user exists in our database
      let user = await this.prisma.user.findUnique({
        where: { firebaseUid }
      });

      if (!user) {
        // Create new user in our database
        user = await this.prisma.user.create({
          data: {
            firebaseUid,
            email,
            name: displayName || email.split('@')[0], // Use displayName or fallback to email prefix
            displayName,
            photoURL,
            emailVerified,
            provider,
          }
        });
      } else {
        // Update existing user with latest info
        user = await this.prisma.user.update({
          where: { firebaseUid },
          data: {
            email,
            displayName,
            photoURL,
            emailVerified,
            provider,
            lastLoginAt: new Date(),
          }
        });
      }

      return {
        id: user.id,
        email: user.email,
        displayName: user.displayName || user.name,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
        provider: user.provider,
        firebaseUid: user.firebaseUid || '',
      };
    } catch (error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  async getUserByFirebaseUid(firebaseUid: string): Promise<AuthUser | null> {
    const user = await this.prisma.user.findUnique({
      where: { firebaseUid }
    });

    if (!user) {
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      displayName: user.displayName || user.name,
      photoURL: user.photoURL,
      emailVerified: user.emailVerified,
      provider: user.provider,
      firebaseUid: user.firebaseUid || '',
    };
  }
}
