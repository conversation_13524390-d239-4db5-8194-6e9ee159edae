import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Image, ScrollView, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';

// Define the navigation stack type
type RootStackParamList = {
  ProfileSetup: undefined;
  ConnectWatch: undefined;
};

const ProfileSetup = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const [profilePic, setProfilePic] = useState<string | null>(null);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [weight, setWeight] = useState('');
  const [height, setHeight] = useState('');
  const [gender, setGender] = useState('');
  const [country, setCountry] = useState('');
  const [association, setAssociation] = useState('');
  const [refereeLevel, setRefereeLevel] = useState('');

  // Placeholder for image picker
  const handleAddPicture = () => {
    // TODO: Implement image picker
  };

  const handleNext = () => {
    navigation.navigate('ConnectWatch');
  };

  const InputField = ({ placeholder, value, onChangeText, style = {}, showArrow = false, ...props }: any) => (
    <View style={[styles.inputContainer, style]}>
      <TextInput
        style={styles.input}
        placeholder={placeholder}
        placeholderTextColor="#999"
        value={value}
        onChangeText={onChangeText}
        {...props}
      />
      {showArrow && (
        <Ionicons name="chevron-down" size={20} color="#999" style={styles.arrowIcon} />
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.statusBar} />
      <ScrollView 
        contentContainerStyle={styles.scrollContent} 
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBar} />
          <View style={[styles.progressBarFill, { width: '33%' }]} />
        </View>
        
        <Text style={styles.stepText}>Step 1 of 3</Text>
        <Text style={styles.title}>Let us know about you</Text>
        
        <TouchableOpacity style={styles.picContainer} onPress={handleAddPicture}>
          <View style={styles.placeholderPic}>
            <Ionicons name="camera" size={32} color="#999" />
          </View>
          <Text style={styles.addPicText}>Add Picture</Text>
        </TouchableOpacity>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Personal Info</Text>
          <InputField 
            placeholder="First Name" 
            value={firstName} 
            onChangeText={setFirstName} 
          />
          <InputField 
            placeholder="Last Name" 
            value={lastName} 
            onChangeText={setLastName} 
          />
          <InputField 
            placeholder="Weight" 
            value={weight} 
            onChangeText={setWeight} 
            keyboardType="numeric"
            style={styles.inputWithUnit}
            showArrow={true}
          />
          <InputField 
            placeholder="Height" 
            value={height} 
            onChangeText={setHeight} 
            keyboardType="numeric"
            style={styles.inputWithUnit}
            showArrow={true}
          />
          <InputField 
            placeholder="Gender" 
            value={gender} 
            onChangeText={setGender} 
            showArrow={true}
          />
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Referee Details</Text>
          <InputField 
            placeholder="Country" 
            value={country} 
            onChangeText={setCountry} 
            showArrow={true}
          />
          <InputField 
            placeholder="Football Association" 
            value={association} 
            onChangeText={setAssociation} 
            showArrow={true}
          />
          <InputField 
            placeholder="Referee Level" 
            value={refereeLevel} 
            onChangeText={setRefereeLevel} 
            showArrow={true}
          />
        </View>
        
        <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
          <Text style={styles.nextButtonText}>Next</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  statusBar: {
    height: Platform.OS === 'ios' ? 44 : 0,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
    paddingBottom: 32,
  },
  progressBarContainer: {
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginBottom: 12,
    overflow: 'hidden',
  },
  progressBar: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    backgroundColor: '#4CAF50',
    width: '33%',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
  },
  stepText: {
    color: '#4CAF50',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 24,
    color: '#2E7D32',
  },
  picContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  placeholderPic: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#e0e0e0',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  addPicText: {
    color: '#757575',
    fontSize: 14,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#00000',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 12,
    position: 'relative',
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 14,
    fontSize: 14,
    color: '#333',
  },
  inputWithUnit: {
    paddingRight: 40,
  },
  arrowIcon: {
    position: 'absolute',
    right: 14,
    top: '50%',
    marginTop: -10,
  },
  nextButton: {
    backgroundColor: '#066B36',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
    shadowColor: '#3AA36D',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProfileSetup;