import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MaterialIcons } from '@expo/vector-icons';

// Define the navigation stack type
type RootStackParamList = {
  ProfileSetup: undefined;
  ConnectWatch: undefined;
  Dashboard: undefined;
};

const ConnectWatch = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  // Placeholder handlers for watch connect
  const handleConnect = (brand: string) => {
    // TODO: Implement watch connection logic
  };

  const handleSkip = () => {
    navigation.navigate('Dashboard');
  };

  return (
    <View style={styles.container}>
      <View style={styles.statusBar} />
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBar} />
          <View style={[styles.progressBarFill, { width: '66%' }]} />
        </View>
        
        <Text style={styles.stepText}>Step 2 of 3</Text>
        <Text style={styles.noDevice}>No device connected</Text>
        <Text style={styles.title}>Connect your smartwatch now</Text>
        
        <View style={styles.card}>
          <TouchableOpacity 
            style={styles.watchBtn} 
            onPress={() => handleConnect('Apple Watch')}
            activeOpacity={0.7}
          >
            <Text style={styles.brandText}> WATCH</Text>
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.watchBtn} 
            onPress={() => handleConnect('Garmin')}
            activeOpacity={0.7}
          >
            <Text style={styles.brandText}>GARMIN</Text>
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.watchBtn} 
            onPress={() => handleConnect('Samsung Galaxy Watch')}
            activeOpacity={0.7}
          >
            <Text style={styles.brandText}>SAMSUNG Galaxy Watch</Text>
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.watchBtn} 
            onPress={() => handleConnect('Wear OS')}
            activeOpacity={0.7}
          >
            <Text style={styles.brandText}>Wear OS by Google</Text>
            <MaterialIcons name="chevron-right" size={24} color="#999" />
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity 
          style={styles.skipBtn} 
          onPress={handleSkip}
          activeOpacity={0.7}
        >
          <Text style={styles.skipBtnText}>Skip for now</Text>
        </TouchableOpacity>
        
        <Text style={styles.skipNote}>
          Continue without a watch{"\n"}
          You can connect your watch later in Settings.
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  statusBar: {
    height: Platform.OS === 'ios' ? 44 : 0,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
    paddingBottom: 32,
  },
  progressBarContainer: {
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginBottom: 12,
    overflow: 'hidden',
  },
  progressBar: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    backgroundColor: '#4CAF50',
    width: '66%',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
  },
  stepText: {
    color: '#4CAF50',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 4,
  },
  noDevice: {
    color: '#757575',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 24,
    color: '#2E7D32',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  watchBtn: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  brandText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  skipBtn: {
    backgroundColor: '#757575',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  skipBtnText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  skipNote: {
    textAlign: 'center',
    color: '#757575',
    fontSize: 13,
    lineHeight: 20,
    marginTop: 16,
  },
});

export default ConnectWatch;