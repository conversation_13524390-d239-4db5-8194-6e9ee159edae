const { execSync } = require('child_process');
const path = require('path');
const os = require('os');

// Try to find the debug keystore
const keystorePaths = [
  path.join(os.homedir(), '.android', 'debug.keystore'),
  path.join(os.homedir(), 'AppData', 'Local', 'Android', 'Sdk', 'debug.keystore'),
];

console.log('Looking for debug keystore...');

for (const keystorePath of keystorePaths) {
  try {
    console.log(`Checking: ${keystorePath}`);
    const result = execSync(`keytool -list -v -keystore "${keystorePath}" -alias androiddebugkey -storepass android -keypass android`, { encoding: 'utf8' });
    console.log('Found keystore!');
    console.log(result);
    break;
  } catch (error) {
    console.log(`Not found at: ${keystorePath}`);
  }
}

console.log('\nIf no keystore found, run:');
console.log('keytool -genkey -v -keystore ~/.android/debug.keystore -storepass android -alias androiddebugkey -keypass android -keyalg RSA -keysize 2048 -validity 10000');
