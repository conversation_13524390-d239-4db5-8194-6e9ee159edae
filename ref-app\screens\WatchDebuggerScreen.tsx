import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Header from '../components/Header';

const WatchDebuggerScreen = () => {
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [logs, setLogs] = useState<string[]>([]);

  const handleConnect = () => {
    setIsScanning(true);
    logMessage('Scanning for devices...');
    
    // Simulate device connection
    setTimeout(() => {
      setIsConnected(true);
      setIsScanning(false);
      logMessage('Successfully connected to watch');
    }, 2000);
  };

  const handleDisconnect = () => {
    logMessage('Disconnecting from watch...');
    setIsConnected(false);
    logMessage('Disconnected from watch');
  };

  const logMessage = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev]);
  };

  const handleSyncData = () => {
    logMessage('Starting data sync...');
    // Simulate data sync
    setTimeout(() => {
      logMessage('Data sync completed');
      logMessage('Received 15 new data points');
    }, 1500);
  };

  return (
    <View style={styles.container}>
      <Header title="Watch Debugger" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View style={styles.sectionBox}>
          <Text style={styles.sectionTitle}>Device Status</Text>
          
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Connection Status:</Text>
            <View style={styles.statusValueContainer}>
              <View 
                style={[
                  styles.statusIndicator, 
                  isConnected ? styles.connected : styles.disconnected
                ]} 
              />
              <Text style={styles.statusText}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </Text>
            </View>
          </View>
          
          <View style={styles.buttonRow}>
            <TouchableOpacity 
              style={[styles.button, isConnected && styles.disabledButton]} 
              onPress={handleConnect}
              disabled={isConnected || isScanning}
            >
              {isScanning ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.buttonText}>Connect</Text>
              )}
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.button, styles.disconnectButton, !isConnected && styles.disabledButton]} 
              onPress={handleDisconnect}
              disabled={!isConnected}
            >
              <Text style={[styles.buttonText, styles.disconnectButtonText]}>Disconnect</Text>
            </TouchableOpacity>
          </View>
          
          <TouchableOpacity 
            style={[styles.syncButton, !isConnected && styles.disabledButton]}
            onPress={handleSyncData}
            disabled={!isConnected}
          >
            <MaterialIcons name="sync" size={20} color="#fff" style={styles.syncIcon} />
            <Text style={styles.syncButtonText}>Sync Data</Text>
          </TouchableOpacity>
        </View>
        
        <View style={[styles.sectionBox, styles.logsContainer]}>
          <Text style={styles.sectionTitle}>Debug Logs</Text>
          <View style={styles.logsContent}>
            {logs.length > 0 ? (
              logs.map((log, index) => (
                <Text key={index} style={styles.logText}>
                  {log}
                </Text>
              ))
            ) : (
              <Text style={styles.noLogsText}>No logs available</Text>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    overflow: 'hidden',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    padding: 16,
    marginTop: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#222',
    marginBottom: 16,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  statusLabel: {
    fontSize: 15,
    color: '#555',
  },
  statusValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  connected: {
    backgroundColor: '#4CAF50',
  },
  disconnected: {
    backgroundColor: '#F44336',
  },
  statusText: {
    fontSize: 15,
    fontWeight: '500',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  button: {
    flex: 1,
    backgroundColor: '#066b36',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  disconnectButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#F44336',
  },
  disabledButton: {
    opacity: 0.5,
  },
  buttonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '600',
  },
  disconnectButtonText: {
    color: '#F44336',
  },
  syncButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#2196F3',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  syncIcon: {
    marginRight: 8,
  },
  syncButtonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '600',
  },
  logsContainer: {
    flex: 1,
  },
  logsContent: {
    backgroundColor: '#1E1E1E',
    borderRadius: 8,
    padding: 12,
    minHeight: 200,
    maxHeight: 300,
  },
  logText: {
    fontFamily: 'monospace',
    color: '#4CAF50',
    fontSize: 12,
    lineHeight: 18,
    marginBottom: 4,
  },
  noLogsText: {
    color: '#888',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
});

export default WatchDebuggerScreen;
