import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import DashboardHome from './DashboardHome';
import CalendarScreen from './CalendarScreen';
import { MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import type { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import Manage from './Manage';
import Evaluate from './Evaluate';

const Tab = createBottomTabNavigator();
const { width: screenWidth } = Dimensions.get('window');

const TAB_ICONS = {
  Calendar: (color: string) => <MaterialIcons name="event" size={26} color={color} />,
  DashboardHome: (color: string) => <MaterialIcons name="dashboard" size={26} color={color} />,
  Evaluate: (color: string) => <MaterialCommunityIcons name="chart-bar" size={26} color={color} />,
  Manage: (color: string) => <MaterialIcons name="settings" size={26} color={color} />,
};

const TAB_LABELS = {
  Calendar: 'Calendar',
  DashboardHome: 'Dashboard',
  Evaluate: 'Evaluate',
  Manage: 'Manage',
};

function CustomTabBar({ state, descriptors, navigation }: BottomTabBarProps) {
  const availableWidth = screenWidth - 32; // Account for container padding
  const tabWidth = Math.floor(availableWidth / 4) - 8; // Space for margins between tabs

  return (
    <SafeAreaView edges={["bottom"]} style={styles.safeArea}>
      <View style={styles.tabBarContainer}>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          let label: string;
          if (typeof options.tabBarLabel === 'string') {
            label = options.tabBarLabel;
          } else {
            label = TAB_LABELS[route.name as keyof typeof TAB_LABELS] || route.name;
          }
          const isFocused = state.index === index;
          const renderIcon = TAB_ICONS[route.name as keyof typeof TAB_ICONS];
          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });
            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };
          return (
            <TouchableOpacity
              key={route.key}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              onPress={onPress}
              style={[
                styles.tabItem, 
                { width: tabWidth },
                isFocused ? styles.tabItemActive : styles.tabItemInactive
              ]}
              activeOpacity={0.8}
            >
              {renderIcon(isFocused ? '#066b36' : '#000000')}
              <Text 
                style={[
                  styles.tabLabel, 
                  isFocused ? styles.tabLabelActive : styles.tabLabelInactive
                ]}
                numberOfLines={1}
                adjustsFontSizeToFit={true}
                minimumFontScale={0.8}
              >
                {label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </SafeAreaView>
  );
}

const Dashboard = () => {
  return (
    <Tab.Navigator
      initialRouteName="DashboardHome"
      screenOptions={{ headerShown: false }}
      tabBar={props => <CustomTabBar {...props} />}
    >
      <Tab.Screen name="Calendar" component={CalendarScreen} />
      <Tab.Screen name="DashboardHome" component={DashboardHome} options={{ title: 'Dashboard' }} />
      <Tab.Screen name="Evaluate" component={Evaluate} />
      <Tab.Screen name="Manage" component={Manage} />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: '#fff',
  },
  tabBarContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  tabItem: {
    height: 64,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
    borderRadius: 14,
    borderWidth: 1,
    flexDirection: 'column',
    backgroundColor: '#fff',
  },
  tabItemActive: {
    backgroundColor: '#e9f4e8',
    borderColor: '#066b36',
  },
  tabItemInactive: {
    backgroundColor: '#fff',
    borderColor: '#e0e0e0',
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 2,
    textAlign: 'center',
  },
  tabLabelActive: {
    color: '#066b36',
  },
  tabLabelInactive: {
    color: '#000',
  },
});

export default Dashboard;