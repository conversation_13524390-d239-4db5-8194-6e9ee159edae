import React from 'react';
import { View, StyleSheet, ScrollView, Text, SafeAreaView } from 'react-native';
import Header from '../components/Header';

const GoProScreen = () => {
  return (
    <View style={styles.container}>
      <Header title="Go Pro Now" showBackButton={true} />
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Upgrade to Pro</Text>
        <Text style={styles.text}>
          Unlock all premium features with RefRate Pro!
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
    marginTop: 8,
  },
  text: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
});

export default GoProScreen;
